import { createApp } from 'vue'
import { createPinia } from 'pinia'

import App from './App.vue'
import router from './router'
import ElementPlus from 'element-plus'; // 根据您选择的 UI 框架调整
import 'element-plus/dist/index.css'; // 样式文件
import 'tdesign-vue-next/es/style/index.css';
import formCreate from '@form-create/tdesign'; // 引入 FormCreate
import TDesign from 'tdesign-vue-next';
import FcDesigner from 'form-designer-x';
import ELEMENT from 'element-plus';




const app = createApp(App)
app.use(TDesign);
app.use(ElementPlus); // 挂载 UI 框架
app.use(ELEMENT);
app.use(formCreate); // 挂载 FormCreate
app.use(FcDesigner)
app.use(FcDesigner.formCreate)
app.use(createPinia())
app.use(router)

app.mount('#app')





