import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJSX from '@vitejs/plugin-vue-jsx'
import dts from "vite-plugin-dts";
// https://vite.dev/config/
export default defineConfig({
  plugins: [
    vue({
      template: {
        compilerOptions: {
          isCustomElement: (tag) => tag.startsWith('fc-')
        }
      }
    }),
    vueJSX(),
    dts({
			// 这里定义了需要生成d.ts文件的目录，如果有多个目录，可以使用数组
			// include: ["src/packages/**/*.{vue,ts}"],
		}),
  ],
  // 开发服务器配置
  server: {
    port: 8080,
    // open: true,
    hmr: {
      overlay: false
    }
  },
  // 开发环境优化
  // optimizeDeps: {
  //   include: [
  //     'vue',
  //     'element-plus',
  //     'vuedraggable',
  //     '@form-create/element-ui',
  //     '@form-create/component-wangeditor',
  //     'codemirror',
  //     'js-beautify'
  //   ]
  // },
  build: {
		// 打包后的文件输出目录
		outDir: "dist",
		// cssCodeSplit: true,
		lib: {
			//指定组件编译入口文件
			entry: "./src/index.js",
			// 组件库名称
			name: "form-designer-x",
			// 文件名称
			fileName: "index",
		},
		rollupOptions: {
			// 确保外部化处理那些你不想打包进库的依赖
			external: ["vue"],
			output: {
				exports: "named",
				// 在 UMD 构建模式下为这些外部化的依赖提供一个全局变量
				globals: {
					vue: "Vue",
				},
				// 确保生成的 CSS 文件输出
				// assetFileNames: "assets/[name].[hash].[ext]", // 生成的 CSS 文件放在 assets 文件夹中
			},
		},
	},
  // 路径解析
  resolve: {
    alias: {
      '@': '/src'
    }
  }
})
